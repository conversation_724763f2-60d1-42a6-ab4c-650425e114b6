/**
 * Steganography Library
 * Implements LSB (Least Significant Bit) steganography for hiding and extracting image data
 */

class Steganography {
    constructor() {
        this.HEADER_SIZE = 32; // 32 bits for data length
        this.MAGIC_NUMBER = 0x53544547; // 'STEG' in hex
    }

    /**
     * Generate a pseudo-random sequence based on password
     * @param {string} password - The encryption password
     * @param {number} length - Length of sequence needed
     * @returns {Array} Array of pseudo-random positions
     */
    generateKeySequence(password, length) {
        const sequence = [];
        let seed = this.hashPassword(password);
        
        for (let i = 0; i < length; i++) {
            seed = (seed * 1103515245 + 12345) & 0x7fffffff;
            sequence.push(seed % length);
        }
        
        return sequence;
    }

    /**
     * Simple hash function for password
     * @param {string} password - The password to hash
     * @returns {number} Hash value
     */
    hashPassword(password) {
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    /**
     * Convert number to binary string with specified length
     * @param {number} num - Number to convert
     * @param {number} length - Length of binary string
     * @returns {string} Binary string
     */
    toBinary(num, length = 8) {
        return num.toString(2).padStart(length, '0');
    }

    /**
     * Convert binary string to number
     * @param {string} binary - Binary string
     * @returns {number} Converted number
     */
    fromBinary(binary) {
        return parseInt(binary, 2);
    }

    /**
     * Hide data in image using LSB steganography
     * @param {ImageData} imageData - Canvas image data
     * @param {string} data - Data to hide (JSON string)
     * @param {string} password - Encryption password
     * @returns {ImageData} Modified image data
     */
    hideData(imageData, data, password) {
        const pixels = new Uint8ClampedArray(imageData.data);
        const dataBytes = new TextEncoder().encode(data);
        
        // Create header with magic number and data length
        const header = new ArrayBuffer(8);
        const headerView = new DataView(header);
        headerView.setUint32(0, this.MAGIC_NUMBER, false);
        headerView.setUint32(4, dataBytes.length, false);
        
        // Combine header and data
        const fullData = new Uint8Array(8 + dataBytes.length);
        fullData.set(new Uint8Array(header), 0);
        fullData.set(dataBytes, 8);
        
        // Convert to binary string
        let binaryData = '';
        for (let i = 0; i < fullData.length; i++) {
            binaryData += this.toBinary(fullData[i]);
        }
        
        // Generate key sequence for pixel positions
        const totalPixels = Math.floor(pixels.length / 4);
        const keySequence = this.generateKeySequence(password, totalPixels);
        
        // Check if we have enough space
        if (binaryData.length > totalPixels * 3) {
            throw new Error('Image too small to hide the data');
        }
        
        // Hide data in LSB of RGB channels (skip alpha)
        let dataIndex = 0;
        for (let i = 0; i < keySequence.length && dataIndex < binaryData.length; i++) {
            const pixelIndex = keySequence[i] * 4;
            
            // Hide in R, G, B channels
            for (let channel = 0; channel < 3 && dataIndex < binaryData.length; channel++) {
                const pixelPos = pixelIndex + channel;
                const bit = parseInt(binaryData[dataIndex]);
                
                // Clear LSB and set new bit
                pixels[pixelPos] = (pixels[pixelPos] & 0xFE) | bit;
                dataIndex++;
            }
        }
        
        return new ImageData(pixels, imageData.width, imageData.height);
    }

    /**
     * Extract hidden data from image
     * @param {ImageData} imageData - Canvas image data
     * @param {string} password - Decryption password
     * @returns {string} Extracted data or null if failed
     */
    extractData(imageData, password) {
        const pixels = imageData.data;
        const totalPixels = Math.floor(pixels.length / 4);
        const keySequence = this.generateKeySequence(password, totalPixels);
        
        // Extract header first (64 bits = 8 bytes)
        let headerBinary = '';
        let dataIndex = 0;
        
        for (let i = 0; i < keySequence.length && headerBinary.length < 64; i++) {
            const pixelIndex = keySequence[i] * 4;
            
            for (let channel = 0; channel < 3 && headerBinary.length < 64; channel++) {
                const pixelPos = pixelIndex + channel;
                headerBinary += (pixels[pixelPos] & 1).toString();
                dataIndex++;
            }
        }
        
        // Parse header
        const magicBinary = headerBinary.substring(0, 32);
        const lengthBinary = headerBinary.substring(32, 64);
        
        const magic = this.fromBinary(magicBinary);
        const dataLength = this.fromBinary(lengthBinary);
        
        // Verify magic number
        if (magic !== this.MAGIC_NUMBER) {
            return null; // Invalid password or no hidden data
        }
        
        // Extract actual data
        const totalBitsNeeded = dataLength * 8;
        let dataBinary = '';
        let currentIndex = dataIndex;
        
        for (let i = Math.floor(currentIndex / 3); i < keySequence.length && dataBinary.length < totalBitsNeeded; i++) {
            const pixelIndex = keySequence[i] * 4;
            
            for (let channel = 0; channel < 3 && dataBinary.length < totalBitsNeeded; channel++) {
                if (currentIndex >= 64) { // Skip header bits
                    const pixelPos = pixelIndex + channel;
                    dataBinary += (pixels[pixelPos] & 1).toString();
                }
                currentIndex++;
            }
        }
        
        // Convert binary to bytes
        const dataBytes = new Uint8Array(dataLength);
        for (let i = 0; i < dataLength; i++) {
            const byteBinary = dataBinary.substring(i * 8, (i + 1) * 8);
            if (byteBinary.length === 8) {
                dataBytes[i] = this.fromBinary(byteBinary);
            }
        }
        
        // Decode to string
        try {
            return new TextDecoder().decode(dataBytes);
        } catch (error) {
            return null; // Decoding failed
        }
    }

    /**
     * Create a carrier image with noise pattern
     * @param {number} width - Image width
     * @param {number} height - Image height
     * @param {string} password - Password for generating pattern
     * @returns {ImageData} Generated carrier image
     */
    createCarrierImage(width, height, password) {
        const imageData = new ImageData(width, height);
        const pixels = imageData.data;
        const seed = this.hashPassword(password);
        let rng = seed;
        
        for (let i = 0; i < pixels.length; i += 4) {
            // Generate pseudo-random RGB values
            rng = (rng * 1103515245 + 12345) & 0x7fffffff;
            const r = (rng % 256);
            
            rng = (rng * 1103515245 + 12345) & 0x7fffffff;
            const g = (rng % 256);
            
            rng = (rng * 1103515245 + 12345) & 0x7fffffff;
            const b = (rng % 256);
            
            pixels[i] = r;     // Red
            pixels[i + 1] = g; // Green
            pixels[i + 2] = b; // Blue
            pixels[i + 3] = 255; // Alpha
        }
        
        return imageData;
    }
}
