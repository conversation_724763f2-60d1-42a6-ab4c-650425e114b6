<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Encrypted Image Viewer - Steganography</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="viewer-styles.css">
</head>
<body>
    <div class="viewer-container">
        <header class="viewer-header">
            <div class="header-content">
                <h1>🔐 Encrypted Image Viewer</h1>
                <p>Click on the encrypted image to reveal the hidden content</p>
                <nav class="viewer-nav">
                    <a href="index.html" class="nav-link">← Back to Encryption Tool</a>
                </nav>
            </div>
        </header>

        <main class="viewer-main">
            <!-- Instructions Section -->
            <section class="viewer-instructions">
                <div class="instruction-card">
                    <h2>📋 How to View</h2>
                    <ol>
                        <li><strong>Click the Image:</strong> Click on the encrypted image below to start decryption</li>
                        <li><strong>Enter Password:</strong> Provide the correct password used during encryption</li>
                        <li><strong>View Original:</strong> If the password is correct, the original image will be revealed</li>
                    </ol>
                </div>
            </section>

            <!-- Image Display Section -->
            <section class="viewer-image-section">
                <div class="encrypted-image-container" id="encryptedImageContainer">
                    <div class="image-placeholder" id="imagePlaceholder">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">🖼️</div>
                            <p>No encrypted image loaded</p>
                            <button class="load-image-btn" id="loadImageBtn">Load Encrypted Image</button>
                        </div>
                    </div>
                </div>
                
                <!-- Image Info Panel -->
                <div class="image-info" id="imageInfo" style="display: none;">
                    <h3>Image Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Status:</span>
                            <span class="info-value" id="imageStatus">Encrypted</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Dimensions:</span>
                            <span class="info-value" id="imageDimensions">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Click to decrypt:</span>
                            <span class="info-value">Password required</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Status Messages -->
            <section class="viewer-status">
                <div id="viewerStatusMessage" class="status-message" hidden></div>
            </section>
        </main>

        <!-- Password Modal -->
        <div class="modal-overlay" id="passwordModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🔓 Enter Decryption Password</h3>
                    <button class="modal-close" id="modalClose">&times;</button>
                </div>
                
                <div class="modal-body">
                    <p>Enter the password used to encrypt this image:</p>
                    <div class="password-input-group">
                        <input type="password" id="modalPasswordInput" placeholder="Enter password" maxlength="50" autocomplete="off">
                        <button type="button" id="modalTogglePassword" class="toggle-password">👁️</button>
                    </div>
                    <div class="modal-error" id="modalError" style="display: none;"></div>
                </div>
                
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="modalCancel">Cancel</button>
                    <button class="btn btn-primary" id="modalDecrypt">
                        <span class="btn-text">🔓 Decrypt</span>
                        <span class="btn-loader" hidden>⏳ Decrypting...</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- File Upload Modal -->
        <div class="modal-overlay" id="uploadModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>📁 Load Encrypted Image</h3>
                    <button class="modal-close" id="uploadModalClose">&times;</button>
                </div>
                
                <div class="modal-body">
                    <div class="upload-area-modal" id="uploadAreaModal">
                        <div class="upload-content">
                            <div class="upload-icon">📁</div>
                            <p>Click to select an encrypted image</p>
                            <p class="file-info">PNG files with hidden data</p>
                        </div>
                        <input type="file" id="modalImageInput" accept="image/*" hidden>
                    </div>
                    <div class="modal-file-status" id="modalFileStatus"></div>
                </div>
                
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="uploadModalCancel">Cancel</button>
                </div>
            </div>
        </div>

        <footer class="viewer-footer">
            <p>🔒 Secure image viewing with steganographic decryption</p>
            <div class="footer-links">
                <a href="index.html">Create Encrypted Images</a>
                <span>•</span>
                <a href="#" id="shareLink">Share This Viewer</a>
            </div>
        </footer>
    </div>

    <!-- Hidden canvas for image processing -->
    <canvas id="hiddenCanvas" style="display: none;"></canvas>
    
    <!-- Scripts -->
    <script src="steganography.js"></script>
    <script src="viewer.js"></script>
</body>
</html>
