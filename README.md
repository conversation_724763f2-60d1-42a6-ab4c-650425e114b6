# 🔐 Image Steganography Web Application

A complete web-based steganography application that allows users to hide images within other images using LSB (Least Significant Bit) techniques and decrypt them with password protection.

## 🌟 Features

### Main Encryption Interface (`index.html`)
- **Image Upload**: Drag & drop or click to upload images (JPG, PNG, GIF)
- **Password Protection**: Secure encryption with user-defined passwords
- **LSB Steganography**: Hide original images within noise patterns
- **Real-time Preview**: Side-by-side view of original and encrypted images
- **File Validation**: Format and size validation (10MB limit)
- **Sharing Options**: Share encrypted images to viewer or download

### Encrypted Image Viewer (`viewer.html`)
- **Secure Viewing**: Display encrypted images without revealing originals
- **Interactive Decryption**: Click-to-decrypt with password modal
- **Data Sharing**: Load images via localStorage, URL parameters, or file upload
- **Error Handling**: Clear feedback for incorrect passwords
- **Responsive Design**: Works on desktop and mobile devices

## 🚀 Quick Start

1. **Open the main application**:
   ```
   Open index.html in your web browser
   ```

2. **Encrypt an image**:
   - Upload an image file
   - Set a secure password
   - Click "Encrypt Image"
   - Use sharing options to view in the viewer

3. **View encrypted images**:
   ```
   Open viewer.html in your web browser
   ```
   - Load an encrypted image
   - Click the image to decrypt
   - Enter the correct password

## 📁 File Structure

```
crypto/
├── index.html              # Main encryption interface
├── viewer.html             # Encrypted image viewer
├── styles.css              # Main application styles
├── viewer-styles.css       # Viewer-specific styles
├── steganography.js        # Core steganography algorithms
├── app.js                  # Main application logic
├── viewer.js               # Viewer application logic
├── create-test-image.html  # Test image generator
└── README.md               # This documentation
```

## 🔧 Technical Implementation

### Steganography Algorithm
- **LSB Technique**: Modifies least significant bits of RGB channels
- **Password-based Key**: Generates pseudo-random pixel positions
- **Data Integrity**: Magic number verification and checksums
- **Carrier Generation**: Creates noise patterns for hiding data

### Security Features
- **Password Protection**: Required for both encryption and decryption
- **Data Validation**: Verifies image integrity during decryption
- **Secure Storage**: Uses localStorage for temporary data sharing
- **Error Handling**: Comprehensive validation and user feedback

### Browser Compatibility
- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **HTML5 Features**: Canvas API, File API, localStorage
- **Responsive Design**: Works on desktop and mobile devices

## 🎯 Usage Examples

### Basic Encryption Workflow
1. Upload a personal photo
2. Set password: "MySecretPassword123"
3. Click "Encrypt Image"
4. Share to viewer or download encrypted image

### Viewer Workflow
1. Open viewer.html
2. Load encrypted image (via sharing or file upload)
3. Click the encrypted image
4. Enter password: "MySecretPassword123"
5. View the revealed original image

### Sharing Methods
- **Direct Sharing**: Use "Open in Viewer" button
- **File Download**: Download encrypted PNG file
- **URL Sharing**: Copy shareable viewer links
- **Manual Upload**: Upload encrypted files to viewer

## 🔒 Security Considerations

### Strengths
- Password-based encryption keys
- Pseudo-random data distribution
- Data integrity verification
- No server-side storage required

### Limitations
- **Demo Purpose**: Not suitable for high-security applications
- **Client-side Only**: All processing happens in the browser
- **Password Strength**: Security depends on password complexity
- **File Size**: Limited by browser memory constraints

## 🛠️ Development

### Adding New Features
The application is modular and extensible:

- **Steganography.js**: Add new hiding algorithms
- **App.js**: Extend main application functionality
- **Viewer.js**: Add viewer-specific features
- **Styles**: Customize appearance and responsive behavior

### Testing
1. Use `create-test-image.html` to generate test images
2. Test with various image formats and sizes
3. Verify password protection works correctly
4. Test sharing mechanisms between interfaces

## 📱 Mobile Support

The application is fully responsive and works on mobile devices:
- Touch-friendly interface
- Responsive image display
- Mobile-optimized modals
- Gesture support for image interaction

## 🎨 Customization

### Styling
- Modify `styles.css` for main interface appearance
- Update `viewer-styles.css` for viewer customization
- Change color schemes in CSS gradient definitions

### Functionality
- Adjust file size limits in validation functions
- Modify supported image formats
- Customize password requirements
- Add new sharing methods

## 📄 License

This project is for educational and demonstration purposes. Feel free to use and modify for your own projects.

## 🤝 Contributing

Contributions are welcome! Areas for improvement:
- Additional steganography algorithms
- Enhanced security features
- Better mobile experience
- Performance optimizations
- Additional file format support

---

**⚠️ Important**: This is a demonstration application. For production use cases requiring high security, implement additional security measures and server-side validation.
