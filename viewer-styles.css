/* Viewer-Specific Styles */

.viewer-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.viewer-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px 0;
    margin-bottom: 30px;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
    color: white;
}

.viewer-header h1 {
    font-size: 2.2rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.viewer-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 20px;
}

.viewer-nav {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Main Content */
.viewer-main {
    flex: 1;
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
}

/* Instructions */
.viewer-instructions {
    margin-bottom: 30px;
}

.instruction-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

.instruction-card h2 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.instruction-card ol {
    margin-left: 20px;
    color: #2d3748;
}

.instruction-card li {
    margin-bottom: 8px;
    line-height: 1.6;
}

/* Image Section */
.viewer-image-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.encrypted-image-container {
    text-align: center;
    margin-bottom: 20px;
}

.image-placeholder {
    border: 3px dashed #cbd5e0;
    border-radius: 15px;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.image-placeholder:hover {
    border-color: #667eea;
    background: #edf2f7;
    transform: scale(1.02);
}

.image-placeholder.has-image {
    border: none;
    background: transparent;
    min-height: auto;
}

.image-placeholder.has-image:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.placeholder-content {
    text-align: center;
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.6;
}

.placeholder-content p {
    color: #718096;
    font-size: 1.2rem;
    margin-bottom: 20px;
}

.load-image-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.load-image-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.encrypted-image, .decrypted-image {
    max-width: 100%;
    max-height: 600px;
    object-fit: contain;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.decrypted-image {
    cursor: default;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Image Info Panel */
.image-info {
    background: #f7fafc;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.image-info h3 {
    color: #4a5568;
    margin-bottom: 15px;
    text-align: center;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.info-label {
    font-weight: 600;
    color: #4a5568;
}

.info-value {
    color: #2d3748;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: slideIn 0.3s ease;
}

.modal-header {
    padding: 25px 25px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 20px;
}

.modal-header h3 {
    color: #4a5568;
    margin: 0;
    font-size: 1.4rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.8rem;
    cursor: pointer;
    color: #a0aec0;
    padding: 5px;
    line-height: 1;
}

.modal-close:hover {
    color: #4a5568;
}

.modal-body {
    padding: 0 25px 20px;
}

.modal-body p {
    color: #4a5568;
    margin-bottom: 15px;
    line-height: 1.6;
}

.modal-error {
    background: #fed7d7;
    color: #742a2a;
    padding: 10px 15px;
    border-radius: 8px;
    margin-top: 10px;
    border: 1px solid #fc8181;
    font-size: 0.9rem;
}

.modal-footer {
    padding: 20px 25px 25px;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    border-top: 1px solid #e2e8f0;
}

/* Upload Area in Modal */
.upload-area-modal {
    border: 2px dashed #cbd5e0;
    border-radius: 10px;
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-area-modal:hover {
    border-color: #667eea;
    background: #edf2f7;
}

.modal-file-status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    font-weight: 500;
}

.modal-file-status.success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.modal-file-status.error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #fc8181;
}

/* Footer */
.viewer-footer {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px 0;
    text-align: center;
    color: white;
    margin-top: auto;
}

.footer-links {
    margin-top: 10px;
}

.footer-links a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-links a:hover {
    opacity: 1;
}

.footer-links span {
    margin: 0 10px;
    opacity: 0.6;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .viewer-header h1 {
        font-size: 1.8rem;
    }
    
    .viewer-main {
        padding: 0 15px;
    }
    
    .instruction-card, .viewer-image-section {
        padding: 20px;
    }
    
    .modal-content {
        width: 95%;
    }
    
    .modal-header, .modal-body, .modal-footer {
        padding-left: 20px;
        padding-right: 20px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .modal-footer .btn {
        width: 100%;
    }
}
