<!DOCTYPE html>
<html>
<head>
    <title>Create Test Image</title>
</head>
<body>
    <h1>Test Image Generator</h1>
    <canvas id="testCanvas" width="400" height="300" style="border: 1px solid black;"></canvas>
    <br><br>
    <button onclick="generateImage()">Generate Test Image</button>
    <button onclick="downloadImage()">Download Test Image</button>

    <script>
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');

        function generateImage() {
            // Create a colorful test image
            const gradient = ctx.createLinearGradient(0, 0, 400, 300);
            gradient.addColorStop(0, '#FF6B6B');
            gradient.addColorStop(0.5, '#4ECDC4');
            gradient.addColorStop(1, '#45B7D1');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 300);
            
            // Add some text
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Test Image for', 200, 120);
            ctx.fillText('Steganography', 200, 150);
            ctx.fillText('Demo', 200, 180);
            
            // Add some shapes
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.beginPath();
            ctx.arc(100, 80, 30, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(300, 220, 25, 0, 2 * Math.PI);
            ctx.fill();
        }

        function downloadImage() {
            const link = document.createElement('a');
            link.download = 'test-image.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        // Generate image on load
        generateImage();
    </script>
</body>
</html>
