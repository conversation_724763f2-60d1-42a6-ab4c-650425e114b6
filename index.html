<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Steganography - Secure Image Encryption</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔐 Image Steganography</h1>
            <p>Securely hide and reveal images using steganographic encryption</p>
        </header>

        <main>
            <!-- Instructions Section -->
            <section class="instructions">
                <h2>How to Use</h2>
                <ol>
                    <li><strong>Upload an Image:</strong> Select an image file (JPG, PNG, GIF) to encrypt</li>
                    <li><strong>Set Password:</strong> Enter a secure password for encryption</li>
                    <li><strong>Encrypt:</strong> Click "Encrypt Image" to hide your image using steganography</li>
                    <li><strong>Decrypt:</strong> Enter the correct password and click "Decrypt Image" to reveal the original</li>
                </ol>
            </section>

            <!-- Upload Section -->
            <section class="upload-section">
                <h2>Step 1: Upload Image</h2>
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <div class="upload-icon">📁</div>
                        <p>Click to select an image or drag and drop</p>
                        <p class="file-info">Supported formats: JPG, PNG, GIF (Max: 10MB)</p>
                    </div>
                    <input type="file" id="imageInput" accept="image/*" hidden>
                </div>
                <div class="file-status" id="fileStatus"></div>
            </section>

            <!-- Password Section -->
            <section class="password-section">
                <h2>Step 2: Set Password</h2>
                <div class="password-input-group">
                    <input type="password" id="passwordInput" placeholder="Enter encryption password" maxlength="50">
                    <button type="button" id="togglePassword" class="toggle-password">👁️</button>
                </div>
                <p class="password-hint">Use a strong password (8+ characters recommended)</p>
            </section>

            <!-- Control Buttons -->
            <section class="controls">
                <button id="encryptBtn" class="btn btn-primary" disabled>
                    <span class="btn-text">🔒 Encrypt Image</span>
                    <span class="btn-loader" hidden>⏳ Encrypting...</span>
                </button>
                <button id="decryptBtn" class="btn btn-secondary" disabled>
                    <span class="btn-text">🔓 Decrypt Image</span>
                    <span class="btn-loader" hidden>⏳ Decrypting...</span>
                </button>
            </section>

            <!-- Image Display Section -->
            <section class="image-display">
                <div class="image-container">
                    <div class="image-panel">
                        <h3>Original Image</h3>
                        <div class="image-placeholder" id="originalImageContainer">
                            <p>No image uploaded</p>
                        </div>
                    </div>
                    <div class="image-panel">
                        <h3>Encrypted Image</h3>
                        <div class="image-placeholder" id="encryptedImageContainer">
                            <p>No encrypted image</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Status Messages -->
            <section class="status-section">
                <div id="statusMessage" class="status-message" hidden></div>
            </section>
        </main>

        <footer>
            <p>⚠️ This is a demonstration of steganographic techniques. For production use, implement additional security measures.</p>
        </footer>
    </div>

    <!-- Hidden canvas for image processing -->
    <canvas id="hiddenCanvas" style="display: none;"></canvas>
    
    <script src="steganography.js"></script>
    <script src="app.js"></script>
</body>
</html>
