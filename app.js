/**
 * Image Steganography Application
 * Main application logic for file handling, UI interactions, and steganography operations
 */

class ImageSteganographyApp {
    constructor() {
        this.steganography = new Steganography();
        this.originalImage = null;
        this.encryptedImageData = null;
        this.currentPassword = '';
        
        this.initializeElements();
        this.bindEvents();
    }

    initializeElements() {
        // File upload elements
        this.uploadArea = document.getElementById('uploadArea');
        this.imageInput = document.getElementById('imageInput');
        this.fileStatus = document.getElementById('fileStatus');
        
        // Password elements
        this.passwordInput = document.getElementById('passwordInput');
        this.togglePassword = document.getElementById('togglePassword');
        
        // Control buttons
        this.encryptBtn = document.getElementById('encryptBtn');
        this.decryptBtn = document.getElementById('decryptBtn');
        
        // Image containers
        this.originalImageContainer = document.getElementById('originalImageContainer');
        this.encryptedImageContainer = document.getElementById('encryptedImageContainer');
        
        // Status message
        this.statusMessage = document.getElementById('statusMessage');
        
        // Hidden canvas for processing
        this.hiddenCanvas = document.getElementById('hiddenCanvas');
        this.hiddenCtx = this.hiddenCanvas.getContext('2d');
    }

    bindEvents() {
        // File upload events
        this.uploadArea.addEventListener('click', () => this.imageInput.click());
        this.imageInput.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // Drag and drop events
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        
        // Password events
        this.passwordInput.addEventListener('input', () => this.updateButtonStates());
        this.togglePassword.addEventListener('click', () => this.togglePasswordVisibility());
        
        // Button events
        this.encryptBtn.addEventListener('click', () => this.encryptImage());
        this.decryptBtn.addEventListener('click', () => this.decryptImage());

        // Sharing button events
        const shareViewerBtn = document.getElementById('shareViewerBtn');
        const downloadBtn = document.getElementById('downloadBtn');

        if (shareViewerBtn) {
            shareViewerBtn.addEventListener('click', () => this.shareEncryptedImage());
        }
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadEncryptedImage());
        }
    }

    // File Upload Handling
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    handleDragOver(event) {
        event.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    processFile(file) {
        // Validate file type
        if (!this.isValidImageType(file.type)) {
            this.showFileStatus('Please select a valid image file (JPG, PNG, GIF)', 'error');
            return;
        }

        // Validate file size (10MB limit)
        const maxSize = 10 * 1024 * 1024; // 10MB in bytes
        if (file.size > maxSize) {
            this.showFileStatus('File size must be less than 10MB', 'error');
            return;
        }

        // Load and display image
        this.loadImage(file);
    }

    isValidImageType(mimeType) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        return validTypes.includes(mimeType.toLowerCase());
    }

    loadImage(file) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.originalImage = img;
                this.displayOriginalImage(img);
                this.showFileStatus(`Image loaded: ${file.name} (${this.formatFileSize(file.size)})`, 'success');
                this.updateButtonStates();
                this.clearEncryptedImage();
            };
            img.onerror = () => {
                this.showFileStatus('Failed to load image', 'error');
            };
            img.src = e.target.result;
        };
        
        reader.onerror = () => {
            this.showFileStatus('Failed to read file', 'error');
        };
        
        reader.readAsDataURL(file);
    }

    displayOriginalImage(img) {
        this.originalImageContainer.innerHTML = '';
        const displayImg = img.cloneNode();
        displayImg.style.maxWidth = '100%';
        displayImg.style.maxHeight = '100%';
        displayImg.style.objectFit = 'contain';
        this.originalImageContainer.appendChild(displayImg);
    }

    displayEncryptedImage(imageData) {
        // Create canvas to display encrypted image
        const canvas = document.createElement('canvas');
        canvas.width = imageData.width;
        canvas.height = imageData.height;
        canvas.style.maxWidth = '100%';
        canvas.style.maxHeight = '100%';
        canvas.style.objectFit = 'contain';
        
        const ctx = canvas.getContext('2d');
        ctx.putImageData(imageData, 0, 0);
        
        this.encryptedImageContainer.innerHTML = '';
        this.encryptedImageContainer.appendChild(canvas);
    }

    clearEncryptedImage() {
        this.encryptedImageContainer.innerHTML = '<p>No encrypted image</p>';
        this.encryptedImageData = null;
    }

    // UI Helper Methods
    showFileStatus(message, type) {
        this.fileStatus.textContent = message;
        this.fileStatus.className = `file-status ${type}`;
        this.fileStatus.style.display = 'block';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    togglePasswordVisibility() {
        const type = this.passwordInput.type === 'password' ? 'text' : 'password';
        this.passwordInput.type = type;
        this.togglePassword.textContent = type === 'password' ? '👁️' : '🙈';
    }

    updateButtonStates() {
        const hasImage = this.originalImage !== null;
        const hasPassword = this.passwordInput.value.trim().length > 0;
        const hasEncryptedImage = this.encryptedImageData !== null;
        
        this.encryptBtn.disabled = !(hasImage && hasPassword);
        this.decryptBtn.disabled = !(hasEncryptedImage && hasPassword);
    }

    showStatus(message, type) {
        this.statusMessage.textContent = message;
        this.statusMessage.className = `status-message ${type}`;
        this.statusMessage.hidden = false;
        
        // Auto-hide after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                this.statusMessage.hidden = true;
            }, 5000);
        }
    }

    setButtonLoading(button, isLoading) {
        const textSpan = button.querySelector('.btn-text');
        const loaderSpan = button.querySelector('.btn-loader');
        
        if (isLoading) {
            textSpan.hidden = true;
            loaderSpan.hidden = false;
            button.disabled = true;
        } else {
            textSpan.hidden = false;
            loaderSpan.hidden = true;
            this.updateButtonStates();
        }
    }

    // Canvas Helper Methods
    getImageData(img) {
        this.hiddenCanvas.width = img.width;
        this.hiddenCanvas.height = img.height;
        this.hiddenCtx.drawImage(img, 0, 0);
        return this.hiddenCtx.getImageData(0, 0, img.width, img.height);
    }

    // Encryption/Decryption Methods
    async encryptImage() {
        if (!this.originalImage || !this.passwordInput.value.trim()) {
            this.showStatus('Please select an image and enter a password', 'error');
            return;
        }

        this.setButtonLoading(this.encryptBtn, true);
        this.currentPassword = this.passwordInput.value.trim();

        try {
            // Get original image data
            const originalImageData = this.getImageData(this.originalImage);

            // Create image metadata to hide
            const metadata = {
                width: originalImageData.width,
                height: originalImageData.height,
                timestamp: Date.now(),
                checksum: this.calculateChecksum(originalImageData.data)
            };

            // Convert original image data to base64 for hiding
            const canvas = document.createElement('canvas');
            canvas.width = originalImageData.width;
            canvas.height = originalImageData.height;
            const ctx = canvas.getContext('2d');
            ctx.putImageData(originalImageData, 0, 0);
            const imageDataUrl = canvas.toDataURL('image/png');

            // Combine metadata and image data
            const dataToHide = JSON.stringify({
                metadata: metadata,
                imageData: imageDataUrl
            });

            // Create carrier image (noise pattern)
            const carrierImageData = this.steganography.createCarrierImage(
                originalImageData.width,
                originalImageData.height,
                this.currentPassword
            );

            // Hide the original image data in the carrier
            this.encryptedImageData = this.steganography.hideData(
                carrierImageData,
                dataToHide,
                this.currentPassword
            );

            // Display encrypted image
            this.displayEncryptedImage(this.encryptedImageData);
            this.showStatus('Image encrypted successfully! The original image is now hidden.', 'success');

            // Show sharing controls
            this.showSharingControls();

        } catch (error) {
            console.error('Encryption error:', error);
            this.showStatus(`Encryption failed: ${error.message}`, 'error');
        } finally {
            this.setButtonLoading(this.encryptBtn, false);
        }
    }

    async decryptImage() {
        if (!this.encryptedImageData || !this.passwordInput.value.trim()) {
            this.showStatus('Please encrypt an image first and enter the password', 'error');
            return;
        }

        this.setButtonLoading(this.decryptBtn, true);
        const password = this.passwordInput.value.trim();

        try {
            // Extract hidden data
            const extractedData = this.steganography.extractData(this.encryptedImageData, password);

            if (!extractedData) {
                this.showStatus('Decryption failed: Invalid password or no hidden data found', 'error');
                return;
            }

            // Parse extracted data
            const hiddenContent = JSON.parse(extractedData);
            const { metadata, imageData } = hiddenContent;

            // Verify checksum
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                canvas.width = metadata.width;
                canvas.height = metadata.height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0);
                const recoveredImageData = ctx.getImageData(0, 0, metadata.width, metadata.height);

                const recoveredChecksum = this.calculateChecksum(recoveredImageData.data);
                if (recoveredChecksum === metadata.checksum) {
                    this.displayOriginalImage(img);
                    this.showStatus('Image decrypted successfully! Original image restored.', 'success');
                } else {
                    this.showStatus('Decryption warning: Image data may be corrupted', 'error');
                }
            };

            img.onerror = () => {
                this.showStatus('Failed to restore original image', 'error');
            };

            img.src = imageData;

        } catch (error) {
            console.error('Decryption error:', error);
            this.showStatus('Decryption failed: Invalid password or corrupted data', 'error');
        } finally {
            this.setButtonLoading(this.decryptBtn, false);
        }
    }

    calculateChecksum(data) {
        let checksum = 0;
        for (let i = 0; i < data.length; i += 4) {
            checksum += data[i] + data[i + 1] + data[i + 2]; // Sum RGB values
        }
        return checksum;
    }

    // Data Sharing Methods
    shareEncryptedImage() {
        if (!this.encryptedImageData) {
            this.showStatus('No encrypted image to share', 'error');
            return;
        }

        // Store encrypted image data in localStorage for the viewer
        const imageDataObj = {
            data: Array.from(this.encryptedImageData.data),
            width: this.encryptedImageData.width,
            height: this.encryptedImageData.height
        };

        try {
            localStorage.setItem('encryptedImageData', JSON.stringify(imageDataObj));

            // Open viewer in new tab
            const viewerUrl = 'viewer.html';
            window.open(viewerUrl, '_blank');

            this.showStatus('Encrypted image shared to viewer!', 'success');
        } catch (error) {
            console.error('Failed to share image:', error);
            this.showStatus('Failed to share image', 'error');
        }
    }

    downloadEncryptedImage() {
        if (!this.encryptedImageData) {
            this.showStatus('No encrypted image to download', 'error');
            return;
        }

        // Create canvas and download
        const canvas = document.createElement('canvas');
        canvas.width = this.encryptedImageData.width;
        canvas.height = this.encryptedImageData.height;
        const ctx = canvas.getContext('2d');
        ctx.putImageData(this.encryptedImageData, 0, 0);

        // Create download link
        canvas.toBlob((blob) => {
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'encrypted-image.png';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            this.showStatus('Encrypted image downloaded!', 'success');
        }, 'image/png');
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new ImageSteganographyApp();
});
