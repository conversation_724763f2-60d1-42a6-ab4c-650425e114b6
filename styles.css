/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content */
main {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Sections */
section {
    margin-bottom: 30px;
}

section h2 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.4rem;
}

/* Instructions */
.instructions {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.instructions ol {
    margin-left: 20px;
}

.instructions li {
    margin-bottom: 8px;
}

/* Upload Area */
.upload-area {
    border: 3px dashed #cbd5e0;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-area:hover {
    border-color: #667eea;
    background: #edf2f7;
}

.upload-area.dragover {
    border-color: #667eea;
    background: #e6fffa;
    transform: scale(1.02);
}

.upload-content .upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-content p {
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.file-info {
    color: #718096;
    font-size: 0.9rem !important;
}

.file-status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    font-weight: 500;
}

.file-status.success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.file-status.error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #fc8181;
}

/* Password Section */
.password-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-group input {
    flex: 1;
    padding: 12px 50px 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.password-input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.toggle-password {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 5px;
}

.password-hint {
    margin-top: 8px;
    color: #718096;
    font-size: 0.9rem;
}

/* Buttons */
.controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Sharing Controls */
.sharing-controls {
    text-align: center;
    margin-top: 20px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.sharing-controls h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.sharing-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-width: 160px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
}

.btn-share {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
}

.btn-share:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(237, 137, 54, 0.4);
}

.btn-download {
    background: linear-gradient(135deg, #805ad5, #6b46c1);
    color: white;
}

.btn-download:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(128, 90, 213, 0.4);
}

.btn-link {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(66, 153, 225, 0.4);
    text-decoration: none;
    color: white;
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Image Display */
.image-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.image-panel h3 {
    text-align: center;
    margin-bottom: 15px;
    color: #4a5568;
}

.image-placeholder {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
}

.image-placeholder p {
    color: #a0aec0;
    font-style: italic;
}

.image-placeholder img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

/* Status Messages */
.status-message {
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    margin-top: 20px;
}

.status-message.success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.status-message.error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #fc8181;
}

.status-message.info {
    background: #bee3f8;
    color: #2a4365;
    border: 1px solid #90cdf4;
}

/* Footer */
footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.8;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    main {
        padding: 20px;
    }
    
    .image-container {
        grid-template-columns: 1fr;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading {
    animation: spin 1s linear infinite;
}
