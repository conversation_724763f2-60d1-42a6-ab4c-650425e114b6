/**
 * Image Steganography Viewer Application
 * Handles encrypted image viewing and decryption with modal interface
 */

class ImageViewer {
    constructor() {
        this.steganography = new Steganography();
        this.encryptedImageData = null;
        this.isDecrypted = false;
        
        this.initializeElements();
        this.bindEvents();
        this.loadImageFromStorage();
    }

    initializeElements() {
        // Image display elements
        this.imagePlaceholder = document.getElementById('imagePlaceholder');
        this.encryptedImageContainer = document.getElementById('encryptedImageContainer');
        this.imageInfo = document.getElementById('imageInfo');
        this.imageStatus = document.getElementById('imageStatus');
        this.imageDimensions = document.getElementById('imageDimensions');
        
        // Modal elements
        this.passwordModal = document.getElementById('passwordModal');
        this.modalPasswordInput = document.getElementById('modalPasswordInput');
        this.modalTogglePassword = document.getElementById('modalTogglePassword');
        this.modalDecrypt = document.getElementById('modalDecrypt');
        this.modalCancel = document.getElementById('modalCancel');
        this.modalClose = document.getElementById('modalClose');
        this.modalError = document.getElementById('modalError');
        
        // Upload modal elements
        this.uploadModal = document.getElementById('uploadModal');
        this.uploadAreaModal = document.getElementById('uploadAreaModal');
        this.modalImageInput = document.getElementById('modalImageInput');
        this.uploadModalClose = document.getElementById('uploadModalClose');
        this.uploadModalCancel = document.getElementById('uploadModalCancel');
        this.modalFileStatus = document.getElementById('modalFileStatus');
        
        // Control elements
        this.loadImageBtn = document.getElementById('loadImageBtn');
        this.shareLink = document.getElementById('shareLink');
        this.viewerStatusMessage = document.getElementById('viewerStatusMessage');
        
        // Hidden canvas
        this.hiddenCanvas = document.getElementById('hiddenCanvas');
        this.hiddenCtx = this.hiddenCanvas.getContext('2d');
    }

    bindEvents() {
        // Image click events
        this.imagePlaceholder.addEventListener('click', (e) => this.handleImageClick(e));
        
        // Load image button
        this.loadImageBtn.addEventListener('click', () => this.showUploadModal());
        
        // Password modal events
        this.modalDecrypt.addEventListener('click', () => this.handleDecryption());
        this.modalCancel.addEventListener('click', () => this.hidePasswordModal());
        this.modalClose.addEventListener('click', () => this.hidePasswordModal());
        this.modalTogglePassword.addEventListener('click', () => this.toggleModalPasswordVisibility());
        this.modalPasswordInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.handleDecryption();
        });
        
        // Upload modal events
        this.uploadAreaModal.addEventListener('click', () => this.modalImageInput.click());
        this.modalImageInput.addEventListener('change', (e) => this.handleModalFileSelect(e));
        this.uploadModalClose.addEventListener('click', () => this.hideUploadModal());
        this.uploadModalCancel.addEventListener('click', () => this.hideUploadModal());
        
        // Share link
        this.shareLink.addEventListener('click', (e) => this.handleShareLink(e));
        
        // Modal overlay clicks
        this.passwordModal.addEventListener('click', (e) => {
            if (e.target === this.passwordModal) this.hidePasswordModal();
        });
        this.uploadModal.addEventListener('click', (e) => {
            if (e.target === this.uploadModal) this.hideUploadModal();
        });
        
        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hidePasswordModal();
                this.hideUploadModal();
            }
        });
    }

    // Image Loading and Display
    loadImageFromStorage() {
        // Try to load from localStorage first
        const storedImageData = localStorage.getItem('encryptedImageData');
        if (storedImageData) {
            try {
                const imageData = JSON.parse(storedImageData);
                this.loadEncryptedImageData(imageData);
                return;
            } catch (error) {
                console.error('Failed to load stored image data:', error);
            }
        }
        
        // Try to load from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const imageParam = urlParams.get('image');
        if (imageParam) {
            try {
                const imageData = JSON.parse(decodeURIComponent(imageParam));
                this.loadEncryptedImageData(imageData);
                return;
            } catch (error) {
                console.error('Failed to load image from URL:', error);
            }
        }
    }

    loadEncryptedImageData(imageData) {
        this.encryptedImageData = new ImageData(
            new Uint8ClampedArray(imageData.data),
            imageData.width,
            imageData.height
        );
        
        this.displayEncryptedImage();
        this.updateImageInfo();
    }

    displayEncryptedImage() {
        // Create canvas to display encrypted image
        const canvas = document.createElement('canvas');
        canvas.width = this.encryptedImageData.width;
        canvas.height = this.encryptedImageData.height;
        canvas.className = 'encrypted-image';
        
        const ctx = canvas.getContext('2d');
        ctx.putImageData(this.encryptedImageData, 0, 0);
        
        // Clear placeholder and add image
        this.imagePlaceholder.innerHTML = '';
        this.imagePlaceholder.appendChild(canvas);
        this.imagePlaceholder.classList.add('has-image');
        
        // Show image info
        this.imageInfo.style.display = 'block';
    }

    displayDecryptedImage(img) {
        // Clear placeholder and add decrypted image
        this.imagePlaceholder.innerHTML = '';
        
        const displayImg = img.cloneNode();
        displayImg.className = 'decrypted-image';
        displayImg.style.maxWidth = '100%';
        displayImg.style.maxHeight = '600px';
        displayImg.style.objectFit = 'contain';
        
        this.imagePlaceholder.appendChild(displayImg);
        this.imagePlaceholder.classList.add('has-image');
        
        // Update status
        this.isDecrypted = true;
        this.updateImageInfo();
    }

    updateImageInfo() {
        if (this.encryptedImageData) {
            this.imageDimensions.textContent = `${this.encryptedImageData.width} × ${this.encryptedImageData.height}`;
            this.imageStatus.textContent = this.isDecrypted ? 'Decrypted' : 'Encrypted';
            this.imageStatus.style.color = this.isDecrypted ? '#22543d' : '#744210';
        }
    }

    // Modal Management
    showPasswordModal() {
        this.passwordModal.style.display = 'flex';
        this.modalPasswordInput.value = '';
        this.modalError.style.display = 'none';
        setTimeout(() => this.modalPasswordInput.focus(), 100);
    }

    hidePasswordModal() {
        this.passwordModal.style.display = 'none';
        this.setModalButtonLoading(false);
    }

    showUploadModal() {
        this.uploadModal.style.display = 'flex';
        this.modalFileStatus.textContent = '';
        this.modalFileStatus.className = 'modal-file-status';
    }

    hideUploadModal() {
        this.uploadModal.style.display = 'none';
    }

    toggleModalPasswordVisibility() {
        const type = this.modalPasswordInput.type === 'password' ? 'text' : 'password';
        this.modalPasswordInput.type = type;
        this.modalTogglePassword.textContent = type === 'password' ? '👁️' : '🙈';
    }

    setModalButtonLoading(isLoading) {
        const textSpan = this.modalDecrypt.querySelector('.btn-text');
        const loaderSpan = this.modalDecrypt.querySelector('.btn-loader');
        
        if (isLoading) {
            textSpan.hidden = true;
            loaderSpan.hidden = false;
            this.modalDecrypt.disabled = true;
        } else {
            textSpan.hidden = false;
            loaderSpan.hidden = true;
            this.modalDecrypt.disabled = false;
        }
    }

    // Event Handlers
    handleImageClick(event) {
        if (!this.encryptedImageData) {
            this.showUploadModal();
            return;
        }
        
        if (this.isDecrypted) {
            // Image is already decrypted, no action needed
            return;
        }
        
        // Show password modal for decryption
        this.showPasswordModal();
    }

    async handleDecryption() {
        const password = this.modalPasswordInput.value.trim();
        
        if (!password) {
            this.showModalError('Please enter a password');
            return;
        }
        
        this.setModalButtonLoading(true);
        this.modalError.style.display = 'none';
        
        try {
            // Extract hidden data
            const extractedData = this.steganography.extractData(this.encryptedImageData, password);
            
            if (!extractedData) {
                this.showModalError('Invalid password or no hidden data found');
                return;
            }
            
            // Parse extracted data
            const hiddenContent = JSON.parse(extractedData);
            const { metadata, imageData } = hiddenContent;
            
            // Load and display original image
            const img = new Image();
            img.onload = () => {
                this.displayDecryptedImage(img);
                this.hidePasswordModal();
                this.showStatus('Image decrypted successfully! Original image revealed.', 'success');
            };
            
            img.onerror = () => {
                this.showModalError('Failed to load decrypted image');
            };
            
            img.src = imageData;
            
        } catch (error) {
            console.error('Decryption error:', error);
            this.showModalError('Decryption failed: Invalid password or corrupted data');
        } finally {
            this.setModalButtonLoading(false);
        }
    }

    handleModalFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.processUploadedFile(file);
        }
    }

    processUploadedFile(file) {
        if (!this.isValidImageType(file.type)) {
            this.showModalFileStatus('Please select a valid image file', 'error');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                // Convert to ImageData
                this.hiddenCanvas.width = img.width;
                this.hiddenCanvas.height = img.height;
                this.hiddenCtx.drawImage(img, 0, 0);
                const imageData = this.hiddenCtx.getImageData(0, 0, img.width, img.height);
                
                this.encryptedImageData = imageData;
                this.isDecrypted = false;
                
                this.displayEncryptedImage();
                this.hideUploadModal();
                this.showStatus('Encrypted image loaded successfully', 'success');
                
                // Store in localStorage for persistence
                this.storeImageData(imageData);
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    handleShareLink(event) {
        event.preventDefault();
        
        if (!this.encryptedImageData) {
            this.showStatus('No encrypted image to share', 'error');
            return;
        }
        
        // Create shareable URL
        const imageDataObj = {
            data: Array.from(this.encryptedImageData.data),
            width: this.encryptedImageData.width,
            height: this.encryptedImageData.height
        };
        
        const encodedData = encodeURIComponent(JSON.stringify(imageDataObj));
        const shareUrl = `${window.location.origin}${window.location.pathname}?image=${encodedData}`;
        
        // Copy to clipboard
        navigator.clipboard.writeText(shareUrl).then(() => {
            this.showStatus('Share link copied to clipboard!', 'success');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = shareUrl;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showStatus('Share link copied to clipboard!', 'success');
        });
    }

    // Helper Methods
    isValidImageType(mimeType) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        return validTypes.includes(mimeType.toLowerCase());
    }

    storeImageData(imageData) {
        try {
            const imageDataObj = {
                data: Array.from(imageData.data),
                width: imageData.width,
                height: imageData.height
            };
            localStorage.setItem('encryptedImageData', JSON.stringify(imageDataObj));
        } catch (error) {
            console.error('Failed to store image data:', error);
        }
    }

    showModalError(message) {
        this.modalError.textContent = message;
        this.modalError.style.display = 'block';
    }

    showModalFileStatus(message, type) {
        this.modalFileStatus.textContent = message;
        this.modalFileStatus.className = `modal-file-status ${type}`;
    }

    showStatus(message, type) {
        this.viewerStatusMessage.textContent = message;
        this.viewerStatusMessage.className = `status-message ${type}`;
        this.viewerStatusMessage.hidden = false;
        
        // Auto-hide after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                this.viewerStatusMessage.hidden = true;
            }, 5000);
        }
    }
}

// Initialize the viewer when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.viewer = new ImageViewer();
});
